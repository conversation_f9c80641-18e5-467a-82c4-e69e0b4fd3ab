import React, { useState, useEffect, useRef, useCallback } from 'react';
import { createPortal } from 'react-dom';
import { useTranslation } from 'react-i18next';
import {
  Checkbox,
  Typography,
  Icon,
} from '@/shared/components/common';
import { useCustomFieldSearch, CustomFieldSearchItem } from '../hooks/useCustomFieldSearch';

export interface CustomFieldData {
  id: number;
  label: string;
  component: string;
  configId?: string;
  type: string;
  required: boolean;
}

interface SimpleCustomFieldSelectorProps {
  onFieldSelect: (fieldData: CustomFieldData) => void;
  selectedFieldIds: number[];
  placeholder?: string;
}

/**
 * Component đơn giản để chọn custom fields với cache và debounce
 */
const SimpleCustomFieldSelector: React.FC<SimpleCustomFieldSelectorProps> = ({
  onFieldSelect,
  selectedFieldIds,
  placeholder = 'Nhập từ khóa và nhấn Enter để tìm kiếm...',
}) => {
  const { t } = useTranslation(['business', 'common']);
  const [isOpen, setIsOpen] = useState(false);
  const [inputValue, setInputValue] = useState('');

  const containerRef = useRef<HTMLDivElement>(null);
  const scrollRef = useRef<HTMLDivElement>(null);
  const [dropdownPosition, setDropdownPosition] = useState<{
    top: number;
    left: number;
    width: number;
  } | null>(null);

  // Sử dụng custom hook để quản lý data
  const {
    items,
    loading,
    hasMore,
    search,
    loadMore,
    initialLoad,
  } = useCustomFieldSearch({
    pageSize: 20,
  });

  // Debug items changes
  useEffect(() => {
    console.log('🎯 Items updated in component:', items.length, items);
  }, [items]);

  // Debug loading changes
  useEffect(() => {
    console.log('🎯 Loading state changed:', loading);
  }, [loading]);

  // Calculate dropdown position
  const calculateDropdownPosition = useCallback(() => {
    if (!containerRef.current) return null;

    const rect = containerRef.current.getBoundingClientRect();
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
    const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;

    return {
      top: rect.bottom + scrollTop + 4, // 4px gap
      left: rect.left + scrollLeft,
      width: rect.width,
    };
  }, []);

  // Handle click outside to close dropdown and scroll/resize events
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
        setIsOpen(false);
        setDropdownPosition(null);
      }
    };

    const handleScroll = () => {
      if (isOpen) {
        const position = calculateDropdownPosition();
        setDropdownPosition(position);
      }
    };

    const handleResize = () => {
      if (isOpen) {
        const position = calculateDropdownPosition();
        setDropdownPosition(position);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    window.addEventListener('scroll', handleScroll, true);
    window.addEventListener('resize', handleResize);

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      window.removeEventListener('scroll', handleScroll, true);
      window.removeEventListener('resize', handleResize);
    };
  }, [isOpen, calculateDropdownPosition]);

  // Handle input focus
  const handleInputFocus = () => {
    setIsOpen(true);
    // Calculate dropdown position
    const position = calculateDropdownPosition();
    setDropdownPosition(position);
    // Load initial data if not loaded yet
    initialLoad();
  };

  // Handle search input change - chỉ update UI, không gọi API
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    console.log('🎯 Input changed to:', value);
    setInputValue(value);
    // Không gọi search() ở đây nữa
  };

  // Handle key down - gọi API khi Enter
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      e.stopPropagation();
      console.log('🎯 Enter pressed, searching for:', inputValue);
      search(inputValue); // Gọi API khi Enter
    }
  };

  // Handle item selection
  const handleItemSelect = (item: CustomFieldSearchItem) => {
    const fieldData: CustomFieldData = {
      id: item.id,
      label: item.label,
      component: item.component,
      configId: item.configId,
      type: item.type,
      required: item.required,
    };

    onFieldSelect(fieldData);
  };

  // Handle scroll for infinite loading
  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    const { scrollTop, scrollHeight, clientHeight } = e.currentTarget;

    if (
      scrollHeight - scrollTop <= clientHeight + 50 && // 50px threshold
      hasMore &&
      !loading
    ) {
      loadMore();
    }
  };

  return (
    <div ref={containerRef} className="relative w-full mb-4">
      {/* Input */}
      <div
        className={`
          flex items-center
          rounded-md
          bg-white dark:bg-gray-800
          py-2 px-3
          cursor-pointer
          transition-all duration-200
          ${isOpen ? 'ring-2 ring-primary/30' : 'hover:bg-gray-50 dark:hover:bg-gray-700'}
        `}
        onClick={() => {
          const newIsOpen = !isOpen;
          setIsOpen(newIsOpen);
          if (newIsOpen) {
            const position = calculateDropdownPosition();
            setDropdownPosition(position);
          } else {
            setDropdownPosition(null);
          }
        }}
      >
        <div className="flex-shrink-0 mr-2 text-gray-500">
          <Icon name="search" size="sm" />
        </div>

        <input
          type="text"
          value={inputValue}
          onChange={handleSearchChange}
          onFocus={handleInputFocus}
          onKeyDown={handleKeyDown}
          onClick={(e) => e.stopPropagation()}
          placeholder={placeholder}
          className="w-full bg-transparent border-none focus:outline-none focus:ring-0 text-foreground placeholder-gray-500"
        />

        {inputValue && (
          <div className="flex-shrink-0 ml-2 text-xs text-gray-400 bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded">
            Enter
          </div>
        )}

        <div className="flex-shrink-0 ml-2 text-gray-500">
          <Icon name={isOpen ? "chevron-up" : "chevron-down"} size="sm" />
        </div>
      </div>

      {/* Dropdown Portal */}
      {isOpen && dropdownPosition && createPortal(
        <div className="fixed z-[99999] bg-white dark:bg-gray-800 rounded-lg shadow-xl overflow-hidden"
             style={{
               top: `${dropdownPosition.top}px`,
               left: `${dropdownPosition.left}px`,
               width: `${dropdownPosition.width}px`,
             }}>
          {/* Header */}
          <div className="bg-gradient-to-r from-red-500 to-orange-500 p-3 text-white">
            <Typography variant="subtitle2" className="font-medium">
              {t('business:product.form.customFields.title', 'Trường tùy chỉnh')}
            </Typography>
          </div>

          {/* Content */}
          <div
            ref={scrollRef}
            className="overflow-auto p-2"
            style={{ maxHeight: '300px' }}
            onScroll={handleScroll}
          >
            {loading && items.length === 0 ? (
              <div className="flex items-center justify-center p-4 text-gray-500">
                <div className="animate-spin mr-2">
                  <Icon name="loader" size="sm" />
                </div>
                <span>{t('common:loading', 'Đang tải...')}</span>
              </div>
            ) : items.length === 0 ? (
              <div className="text-center py-4 text-gray-500">
                {t('common:noResults', 'Không có kết quả')}
              </div>
            ) : (
              <div className="space-y-1">
                {items.map((item) => {
                  const isSelected = selectedFieldIds.includes(item.id);

                  return (
                    <div
                      key={item.id}
                      className={`
                        flex items-center p-2 rounded-lg cursor-pointer
                        hover:bg-gray-50 dark:hover:bg-gray-700
                        transition-colors duration-150
                        ${isSelected ? 'bg-blue-50 dark:bg-blue-900/20' : ''}
                      `}
                      onClick={() => handleItemSelect(item)}
                    >
                      <div
                        onClick={(e) => {
                          e.stopPropagation();
                          handleItemSelect(item);
                        }}
                        className="mr-3"
                      >
                        <Checkbox
                          checked={isSelected}
                          onChange={() => handleItemSelect(item)}
                          variant="filled"
                        />
                      </div>

                      <div className="flex-grow">
                        <div className="flex items-center justify-between">
                          <Typography variant="body2" className="font-medium">
                            {item.label}
                          </Typography>
                          {item.required && (
                            <span className="text-red-500 text-xs ml-2">*</span>
                          )}
                        </div>

                        <div className="flex items-center space-x-2 mt-1">
                          <span className="text-xs bg-gray-200 dark:bg-gray-600 px-2 py-1 rounded">
                            {item.component}
                          </span>
                          {item.configId && (
                            <span className="text-xs bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300 px-2 py-1 rounded">
                              {item.configId}
                            </span>
                          )}
                          <span className="text-xs text-gray-500">
                            {item.type}
                          </span>
                        </div>
                      </div>
                    </div>
                  );
                })}

                {/* Loading more indicator */}
                {loading && items.length > 0 && (
                  <div className="flex items-center justify-center p-2 text-gray-500">
                    <div className="animate-spin mr-2">
                      <Icon name="loader" size="xs" />
                    </div>
                    <span className="text-xs">{t('common:loadingMore', 'Đang tải thêm...')}</span>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>,
        document.body
      )}
    </div>
  );
};

export default SimpleCustomFieldSelector;
