import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { WarehouseService } from '../services/warehouse.service';
import {
  CreateWarehouseDto,
  UpdateWarehouseDto,
  WarehouseQueryParams,
  PhysicalWarehouseDto,
  QueryPhysicalWarehouseDto,
  PhysicalWarehouseListResponse,
  WarehouseLoadOptionsResponse,
  CreatePhysicalWarehouseDto,
  UpdatePhysicalWarehouseDto,
} from '../types/warehouse.types';

// Định nghĩa các query key
export const WAREHOUSE_QUERY_KEYS = {
  all: ['warehouses'] as const,
  lists: () => [...WAREHOUSE_QUERY_KEYS.all, 'list'] as const,
  list: (filters: WarehouseQueryParams) => [...WAREHOUSE_QUERY_KEYS.lists(), filters] as const,
  details: () => [...WAREHOUSE_QUERY_KEYS.all, 'detail'] as const,
  detail: (id: number) => [...WAREHOUSE_QUERY_KEYS.details(), id] as const,

  // Physical warehouse keys
  PHYSICAL: ['warehouses', 'physical'] as const,
  PHYSICAL_LIST: (params: QueryPhysicalWarehouseDto) =>
    [...WAREHOUSE_QUERY_KEYS.PHYSICAL, 'list', params] as const,
  PHYSICAL_DETAIL: (id: number) =>
    [...WAREHOUSE_QUERY_KEYS.PHYSICAL, 'detail', id] as const,
  PHYSICAL_SELECT: (params: { search?: string; page?: number; limit?: number }) =>
    [...WAREHOUSE_QUERY_KEYS.PHYSICAL, 'select', params] as const,
};

/**
 * Hook để lấy danh sách kho
 * @param params Tham số truy vấn
 * @returns Query object
 */
export const useWarehouses = (params?: WarehouseQueryParams) => {
  return useQuery({
    queryKey: WAREHOUSE_QUERY_KEYS.list(params || { page: 1, limit: 10 }),
    queryFn: () => WarehouseService.getWarehouses(params),
  });
};

/**
 * Hook để lấy chi tiết kho theo ID
 * @param id ID của kho
 * @returns Query object
 */
export const useWarehouse = (id: number) => {
  return useQuery({
    queryKey: WAREHOUSE_QUERY_KEYS.detail(id),
    queryFn: () => WarehouseService.getWarehouseById(id),
    enabled: !!id,
  });
};

/**
 * Hook để tạo kho mới
 * @returns Mutation object
 */
export const useCreateWarehouse = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateWarehouseDto) => WarehouseService.createWarehouse(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: WAREHOUSE_QUERY_KEYS.lists() });
    },
  });
};

/**
 * Hook để cập nhật kho
 * @returns Mutation object
 */
export const useUpdateWarehouse = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: UpdateWarehouseDto }) =>
      WarehouseService.updateWarehouse(id, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: WAREHOUSE_QUERY_KEYS.detail(variables.id) });
      queryClient.invalidateQueries({ queryKey: WAREHOUSE_QUERY_KEYS.lists() });
    },
  });
};

/**
 * Hook để xóa kho
 * @returns Mutation object
 */
export const useDeleteWarehouse = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: number) => WarehouseService.deleteWarehouse(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: WAREHOUSE_QUERY_KEYS.lists() });
    },
  });
};

// ===== PHYSICAL WAREHOUSE HOOKS =====

/**
 * Hook để lấy danh sách kho vật lý
 */
export const usePhysicalWarehouses = (
  params?: QueryPhysicalWarehouseDto,
  options?: {
    enabled?: boolean;
    staleTime?: number;
    cacheTime?: number;
  }
) => {
  return useQuery({
    queryKey: WAREHOUSE_QUERY_KEYS.PHYSICAL_LIST(params || {}),
    queryFn: () => WarehouseService.getPhysicalWarehouses(params),
    enabled: options?.enabled ?? true,
    staleTime: options?.staleTime ?? 5 * 60 * 1000, // 5 minutes
    cacheTime: options?.cacheTime ?? 10 * 60 * 1000, // 10 minutes
  });
};

/**
 * Hook để lấy thông tin kho vật lý theo ID
 */
export const usePhysicalWarehouse = (
  warehouseId: number,
  options?: {
    enabled?: boolean;
    staleTime?: number;
    cacheTime?: number;
  }
) => {
  return useQuery({
    queryKey: WAREHOUSE_QUERY_KEYS.PHYSICAL_DETAIL(warehouseId),
    queryFn: () => WarehouseService.getPhysicalWarehouseById(warehouseId),
    enabled: (options?.enabled ?? true) && !!warehouseId && warehouseId > 0,
    staleTime: options?.staleTime ?? 5 * 60 * 1000, // 5 minutes
    cacheTime: options?.cacheTime ?? 10 * 60 * 1000, // 10 minutes
  });
};

/**
 * Hook để lấy danh sách kho cho select component
 */
export const useWarehousesForSelect = (
  params: {
    search?: string;
    page?: number;
    limit?: number;
  },
  options?: {
    enabled?: boolean;
    staleTime?: number;
    cacheTime?: number;
  }
) => {
  return useQuery({
    queryKey: WAREHOUSE_QUERY_KEYS.PHYSICAL_SELECT(params),
    queryFn: () => WarehouseService.getWarehousesForSelect(params),
    enabled: options?.enabled ?? true,
    staleTime: options?.staleTime ?? 2 * 60 * 1000, // 2 minutes
    cacheTime: options?.cacheTime ?? 5 * 60 * 1000, // 5 minutes
  });
};

/**
 * Hook để tạo kho vật lý mới
 */
export const useCreatePhysicalWarehouse = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreatePhysicalWarehouseDto) =>
      WarehouseService.createPhysicalWarehouse(data),
    onSuccess: () => {
      // Invalidate và refetch danh sách kho
      queryClient.invalidateQueries({ queryKey: WAREHOUSE_QUERY_KEYS.PHYSICAL });
    },
  });
};

/**
 * Hook để cập nhật kho vật lý
 */
export const useUpdatePhysicalWarehouse = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ warehouseId, data }: {
      warehouseId: number;
      data: UpdatePhysicalWarehouseDto
    }) => WarehouseService.updatePhysicalWarehouse(warehouseId, data),
    onSuccess: (_, { warehouseId }) => {
      // Invalidate danh sách và detail của kho đã cập nhật
      queryClient.invalidateQueries({ queryKey: WAREHOUSE_QUERY_KEYS.PHYSICAL });
      queryClient.invalidateQueries({
        queryKey: WAREHOUSE_QUERY_KEYS.PHYSICAL_DETAIL(warehouseId)
      });
    },
  });
};

/**
 * Hook để xóa kho vật lý
 */
export const useDeletePhysicalWarehouse = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (warehouseId: number) =>
      WarehouseService.deletePhysicalWarehouse(warehouseId),
    onSuccess: (_, warehouseId) => {
      // Invalidate danh sách và xóa cache detail
      queryClient.invalidateQueries({ queryKey: WAREHOUSE_QUERY_KEYS.PHYSICAL });
      queryClient.removeQueries({
        queryKey: WAREHOUSE_QUERY_KEYS.PHYSICAL_DETAIL(warehouseId)
      });
    },
  });
};
