import { QueryDto, SortDirection } from '@/shared/dto/request/query.dto';
import { PaginatedResult } from '@/shared/dto/response/paginated-result.dto';

/**
 * Enum cho trạng thái kho
 */
export enum WarehouseStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
}

/**
 * Enum định nghĩa các loại kho
 */
export enum WarehouseTypeEnum {
  /**
   * Kho vật lý
   */
  PHYSICAL = 'PHYSICAL',

  /**
   * Kho ảo
   */
  VIRTUAL = 'VIRTUAL',
}

/**
 * Interface cho thông tin địa chỉ kho
 */
export interface WarehouseAddress {
  street?: string;
  district?: string;
  city?: string;
  province?: string;
  country?: string;
  postalCode?: string;
}

/**
 * Interface cho thông tin liên hệ kho
 */
export interface WarehouseContact {
  name?: string;
  phone?: string;
  email?: string;
  position?: string;
}

/**
 * Interface cho thông tin kho
 */
export interface WarehouseDto {
  id: number;
  name: string;
  code: string;
  description?: string;
  address?: WarehouseAddress;
  contact?: WarehouseContact;
  status: WarehouseStatus;
  userId: number;
  createdAt: string;
  updatedAt: string;
}

/**
 * Interface cho danh sách kho
 */
export interface WarehouseListItemDto {
  warehouseId: number;
  name: string;
  description?: string;
  type?: string;
  code?: string;
  status?: WarehouseStatus;
  createdAt?: string;
  updatedAt?: string;
}

/**
 * Interface cho chi tiết kho
 */
export interface WarehouseDetailDto extends WarehouseDto {
  customFields?: WarehouseCustomFieldDto[];
  type?: WarehouseTypeEnum;
}

/**
 * Interface cho trường tùy chỉnh của kho
 */
export interface WarehouseCustomFieldDto {
  id: number;
  warehouseId: number;
  name: string;
  type: string;
  value: unknown;
  required: boolean;
  order: number;
  createdAt: string;
  updatedAt: string;
}

/**
 * Interface cho tham số truy vấn kho
 */
export interface WarehouseQueryParams extends QueryDto {
  status?: WarehouseStatus;
}

/**
 * Interface cho dữ liệu tạo kho
 */
export interface CreateWarehouseDto {
  name: string;
  description?: string;
  type: WarehouseTypeEnum;
}

/**
 * Interface cho dữ liệu cập nhật kho
 */
export interface UpdateWarehouseDto {
  name?: string;
  code?: string;
  description?: string;
  address?: WarehouseAddress;
  contact?: WarehouseContact;
  status?: WarehouseStatus;
}

/**
 * Interface cho dữ liệu tạo trường tùy chỉnh của kho
 */
export interface CreateWarehouseCustomFieldDto {
  name: string;
  type: string;
  value: unknown;
  required?: boolean;
  order?: number;
}

/**
 * Interface cho dữ liệu cập nhật trường tùy chỉnh của kho
 */
export interface UpdateWarehouseCustomFieldDto {
  name?: string;
  type?: string;
  value?: unknown;
  required?: boolean;
  order?: number;
}

// ===== API SPECIFIC TYPES =====

/**
 * Interface cho response thông tin kho vật lý từ API
 */
export interface PhysicalWarehouseDto {
  warehouseId?: number; // Optional vì API có thể không trả về
  name: string;
  description: string;
  type: WarehouseTypeEnum;
  address: string;
  capacity: number | null;
  customFields?: Array<{
    id: number;
    fieldId: number;
    value: Record<string, unknown>;
  }>;
}

/**
 * Interface cho query parameters danh sách kho vật lý
 */
export interface QueryPhysicalWarehouseDto {
  page?: number;
  limit?: number;
  search?: string;
  userId?: number;
  sortBy?: string;
  sortDirection?: SortDirection;
}

// Sử dụng PaginatedResult từ shared thay vì tự định nghĩa
export type PhysicalWarehouseListResponse = PaginatedResult<PhysicalWarehouseDto>;

/**
 * Interface cho SelectOption tương thích với AsyncSelectWithPagination
 */
export interface WarehouseSelectOption {
  value: string | number;
  label: string;
  disabled?: boolean;
  icon?: string;
  data?: Record<string, unknown>;
}

/**
 * Interface cho response của loadOptions trong AsyncSelectWithPagination
 */
export interface WarehouseLoadOptionsResponse {
  items: WarehouseSelectOption[];
  totalItems: number;
  totalPages: number;
  currentPage: number;
}

/**
 * Interface cho DTO tạo kho vật lý
 */
export interface CreatePhysicalWarehouseDto {
  name: string;
  description?: string;
  address: string;
  capacity?: number;
  userId?: number;
  customFields?: Array<{
    customFieldId: number;
    value: Record<string, unknown>;
  }>;
}

/**
 * Interface cho DTO cập nhật kho vật lý
 */
export interface UpdatePhysicalWarehouseDto {
  name?: string;
  description?: string;
  address?: string;
  capacity?: number;
  customFields?: Array<{
    customFieldId: number;
    value: Record<string, unknown>;
  }>;
}
