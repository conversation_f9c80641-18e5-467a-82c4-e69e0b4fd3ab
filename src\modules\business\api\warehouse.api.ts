import { apiClient } from '@/shared/api/client';
import {
  PhysicalWarehouseDto,
  QueryPhysicalWarehouseDto,
  PhysicalWarehouseListResponse,
  ApiResponse,
  CreatePhysicalWarehouseDto,
  UpdatePhysicalWarehouseDto,
} from '../types/warehouse.types';

/**
 * API endpoints cho quản lý kho vật lý
 */
export const warehouseApi = {
  /**
   * Lấy danh sách kho vật lý với phân trang và lọc
   */
  getPhysicalWarehouses: async (
    params?: QueryPhysicalWarehouseDto
  ): Promise<ApiResponse<PhysicalWarehouseListResponse>> => {
    const response = await apiClient.get('/user/physical-warehouses', { params });
    return response.data;
  },

  /**
   * Lấy thông tin kho vật lý theo ID
   */
  getPhysicalWarehouseById: async (
    warehouseId: number
  ): Promise<ApiResponse<PhysicalWarehouseDto>> => {
    const response = await apiClient.get(`/user/physical-warehouses/${warehouseId}`);
    return response.data;
  },

  /**
   * Tạo mới kho vật lý
   */
  createPhysicalWarehouse: async (
    data: CreatePhysicalWarehouseDto
  ): Promise<ApiResponse<PhysicalWarehouseDto>> => {
    const response = await apiClient.post('/user/physical-warehouses', data);
    return response.data;
  },

  /**
   * Cập nhật thông tin kho vật lý
   */
  updatePhysicalWarehouse: async (
    warehouseId: number,
    data: UpdatePhysicalWarehouseDto
  ): Promise<ApiResponse<PhysicalWarehouseDto>> => {
    const response = await apiClient.put(`/user/physical-warehouses/${warehouseId}`, data);
    return response.data;
  },

  /**
   * Xóa kho vật lý
   */
  deletePhysicalWarehouse: async (
    warehouseId: number
  ): Promise<ApiResponse<{ message: string }>> => {
    const response = await apiClient.delete(`/user/physical-warehouses/${warehouseId}`);
    return response.data;
  },
};
